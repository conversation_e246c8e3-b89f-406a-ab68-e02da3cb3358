"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./components/admin/mobile/MobileHamburgerMenu.tsx":
/*!*********************************************************!*\
  !*** ./components/admin/mobile/MobileHamburgerMenu.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MobileHamburgerMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_gestures_swipe_handler__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/gestures/swipe-handler */ \"./lib/gestures/swipe-handler.ts\");\n/* harmony import */ var _styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../styles/admin/mobile/MobileHamburgerMenu.module.css */ \"./styles/admin/mobile/MobileHamburgerMenu.module.css\");\n/* harmony import */ var _styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5__);\n/**\n * Ocean Soul Sparkles Admin - Mobile Hamburger Menu Component\n * Slide-out mobile menu for additional navigation options\n */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MobileHamburgerMenu(param) {\n    let { isOpen, onClose, userRole, userName } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const menuItems = [\n        // Core Features\n        {\n            id: \"dashboard\",\n            label: \"Dashboard\",\n            icon: \"\\uD83D\\uDCCA\",\n            href: \"/admin/dashboard\",\n            roles: [\n                \"DEV\",\n                \"Admin\",\n                \"Artist\",\n                \"Braider\"\n            ],\n            section: \"Core\"\n        },\n        {\n            id: \"pos\",\n            label: \"Point of Sale\",\n            icon: \"\\uD83D\\uDCB3\",\n            href: \"/admin/pos\",\n            roles: [\n                \"DEV\",\n                \"Admin\",\n                \"Artist\",\n                \"Braider\"\n            ],\n            section: \"Core\"\n        },\n        {\n            id: \"bookings\",\n            label: \"Bookings\",\n            icon: \"\\uD83D\\uDCC5\",\n            href: \"/admin/bookings\",\n            roles: [\n                \"DEV\",\n                \"Admin\",\n                \"Artist\",\n                \"Braider\"\n            ],\n            section: \"Core\"\n        },\n        {\n            id: \"customers\",\n            label: \"Customers\",\n            icon: \"\\uD83D\\uDC65\",\n            href: \"/admin/customers\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Core\"\n        },\n        // Management\n        {\n            id: \"services\",\n            label: \"Services\",\n            icon: \"✂️\",\n            href: \"/admin/services\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Management\"\n        },\n        {\n            id: \"products\",\n            label: \"Products\",\n            icon: \"\\uD83D\\uDECD️\",\n            href: \"/admin/products\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Management\"\n        },\n        {\n            id: \"inventory\",\n            label: \"Inventory\",\n            icon: \"\\uD83D\\uDCE6\",\n            href: \"/admin/inventory\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Management\"\n        },\n        {\n            id: \"staff\",\n            label: \"Staff Management\",\n            icon: \"\\uD83D\\uDC68‍\\uD83D\\uDCBC\",\n            href: \"/admin/staff\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Management\"\n        },\n        // Analytics & Reports\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: \"\\uD83D\\uDCC8\",\n            href: \"/admin/analytics\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Analytics\"\n        },\n        {\n            id: \"reports\",\n            label: \"Reports\",\n            icon: \"\\uD83D\\uDCCB\",\n            href: \"/admin/reports\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Analytics\"\n        },\n        // Settings\n        {\n            id: \"settings\",\n            label: \"Settings\",\n            icon: \"⚙️\",\n            href: \"/admin/settings\",\n            roles: [\n                \"DEV\",\n                \"Admin\"\n            ],\n            section: \"Settings\"\n        },\n        {\n            id: \"profile\",\n            label: \"Profile\",\n            icon: \"\\uD83D\\uDC64\",\n            href: \"/admin/profile\",\n            roles: [\n                \"DEV\",\n                \"Admin\",\n                \"Artist\",\n                \"Braider\"\n            ],\n            section: \"Settings\"\n        }\n    ];\n    // Close menu on route change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleRouteChange = ()=>{\n            onClose();\n        };\n        router.events.on(\"routeChangeStart\", handleRouteChange);\n        return ()=>{\n            router.events.off(\"routeChangeStart\", handleRouteChange);\n        };\n    }, [\n        router.events,\n        onClose\n    ]);\n    // Close menu on escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleEscape = (e)=>{\n            if (e.key === \"Escape\" && isOpen) {\n                onClose();\n            }\n        };\n        document.addEventListener(\"keydown\", handleEscape);\n        return ()=>document.removeEventListener(\"keydown\", handleEscape);\n    }, [\n        isOpen,\n        onClose\n    ]);\n    // Prevent body scroll when menu is open\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            document.body.style.overflow = \"hidden\";\n        } else {\n            document.body.style.overflow = \"\";\n        }\n        return ()=>{\n            document.body.style.overflow = \"\";\n        };\n    }, [\n        isOpen\n    ]);\n    const handleMenuItemClick = (item)=>{\n        try {\n            console.log(\"\\uD83D\\uDD17 Mobile menu item clicked:\", item.label, item.href);\n            _lib_gestures_swipe_handler__WEBPACK_IMPORTED_MODULE_4__.HapticFeedback.light();\n            onClose();\n        } catch (error) {\n            console.error(\"❌ Error in mobile menu item click:\", error);\n            onClose(); // Still close menu even if there's an error\n        }\n    };\n    const handleBackdropClick = (e)=>{\n        if (e.target === e.currentTarget) {\n            _lib_gestures_swipe_handler__WEBPACK_IMPORTED_MODULE_4__.HapticFeedback.light();\n            onClose();\n        }\n    };\n    const isActive = (href)=>{\n        if (href === \"/admin/dashboard\") {\n            return router.pathname === \"/admin/dashboard\" || router.pathname === \"/admin\";\n        }\n        return router.pathname.startsWith(href);\n    };\n    const filteredMenuItems = menuItems.filter((item)=>item.roles.includes(userRole));\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D Mobile Menu Debug:\", {\n        userRole,\n        totalMenuItems: menuItems.length,\n        filteredMenuItems: filteredMenuItems.length,\n        filteredItems: filteredMenuItems.map((item)=>item.label)\n    });\n    const groupedItems = filteredMenuItems.reduce((groups, item)=>{\n        const section = item.section || \"Other\";\n        if (!groups[section]) {\n            groups[section] = [];\n        }\n        groups[section].push(item);\n        return groups;\n    }, {});\n    if (!isOpen) return null;\n    // Error boundary for menu rendering\n    try {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuOverlay), \" \").concat(isOpen ? (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().open) : \"\"),\n            onClick: handleBackdropClick,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userInfo),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userAvatar),\n                                        children: userName.charAt(0).toUpperCase()\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userDetails),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userName),\n                                                children: userName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userRole),\n                                                children: userRole\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().closeButton),\n                                onClick: onClose,\n                                \"aria-label\": \"Close menu\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuContent),\n                        children: Object.entries(groupedItems).map((param)=>{\n                            let [section, items] = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuSection),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().sectionTitle),\n                                        children: section\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().sectionItems),\n                                        children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: item.href,\n                                                className: \"\".concat((_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItem), \" \").concat(isActive(item.href) ? (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().active) : \"\"),\n                                                onClick: ()=>handleMenuItemClick(item),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItemIcon),\n                                                        children: item.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItemLabel),\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    isActive(item.href) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().activeIndicator)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, section, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                        lineNumber: 256,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuFooter),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().appInfo),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().appName),\n                                    children: \"Ocean Soul Sparkles Admin\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().appVersion),\n                                    children: \"v1.0.0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n            lineNumber: 230,\n            columnNumber: 7\n        }, this);\n    } catch (error) {\n        console.error(\"❌ Error rendering mobile hamburger menu:\", error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat((_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuOverlay), \" \").concat(isOpen ? (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().open) : \"\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuContainer),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuHeader),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userInfo),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userAvatar),\n                                        children: \"!\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userDetails),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userName),\n                                                children: \"Error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().userRole),\n                                                children: \"Menu Error\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().closeButton),\n                                onClick: onClose,\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuContent),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuSection),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().sectionTitle),\n                                    children: \"Error\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().sectionItems),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItem),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItemIcon),\n                                                children: \"❌\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_mobile_MobileHamburgerMenu_module_css__WEBPACK_IMPORTED_MODULE_5___default().menuItemLabel),\n                                                children: \"Menu failed to load\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\mobile\\\\MobileHamburgerMenu.tsx\",\n            lineNumber: 297,\n            columnNumber: 7\n        }, this);\n    }\n}\n_s(MobileHamburgerMenu, \"wwuRCRXmgHDpG34IuGNQg2ESIb4=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = MobileHamburgerMenu;\nvar _c;\n$RefreshReg$(_c, \"MobileHamburgerMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FkbWluL21vYmlsZS9Nb2JpbGVIYW1idXJnZXJNZW51LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7O0NBR0M7O0FBRXdDO0FBQ1o7QUFDVztBQUM2QjtBQUNZO0FBa0JsRSxTQUFTTSxvQkFBb0IsS0FLakI7UUFMaUIsRUFDMUNDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxRQUFRLEVBQ1JDLFFBQVEsRUFDaUIsR0FMaUI7O0lBTTFDLE1BQU1DLFNBQVNSLHNEQUFTQTtJQUV4QixNQUFNUyxZQUF3QjtRQUM1QixnQkFBZ0I7UUFDaEI7WUFDRUMsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxPQUFPO2dCQUFDO2dCQUFPO2dCQUFTO2dCQUFVO2FBQVU7WUFDNUNDLFNBQVM7UUFDWDtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsT0FBTztnQkFBQztnQkFBTztnQkFBUztnQkFBVTthQUFVO1lBQzVDQyxTQUFTO1FBQ1g7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE9BQU87Z0JBQUM7Z0JBQU87Z0JBQVM7Z0JBQVU7YUFBVTtZQUM1Q0MsU0FBUztRQUNYO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxPQUFPO2dCQUFDO2dCQUFPO2FBQVE7WUFDdkJDLFNBQVM7UUFDWDtRQUVBLGFBQWE7UUFDYjtZQUNFTCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE9BQU87Z0JBQUM7Z0JBQU87YUFBUTtZQUN2QkMsU0FBUztRQUNYO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxPQUFPO2dCQUFDO2dCQUFPO2FBQVE7WUFDdkJDLFNBQVM7UUFDWDtRQUNBO1lBQ0VMLElBQUk7WUFDSkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsT0FBTztnQkFBQztnQkFBTzthQUFRO1lBQ3ZCQyxTQUFTO1FBQ1g7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE9BQU87Z0JBQUM7Z0JBQU87YUFBUTtZQUN2QkMsU0FBUztRQUNYO1FBRUEsc0JBQXNCO1FBQ3RCO1lBQ0VMLElBQUk7WUFDSkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsT0FBTztnQkFBQztnQkFBTzthQUFRO1lBQ3ZCQyxTQUFTO1FBQ1g7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE9BQU87Z0JBQUM7Z0JBQU87YUFBUTtZQUN2QkMsU0FBUztRQUNYO1FBRUEsV0FBVztRQUNYO1lBQ0VMLElBQUk7WUFDSkMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLE1BQU07WUFDTkMsT0FBTztnQkFBQztnQkFBTzthQUFRO1lBQ3ZCQyxTQUFTO1FBQ1g7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLE9BQU87WUFDUEMsTUFBTTtZQUNOQyxNQUFNO1lBQ05DLE9BQU87Z0JBQUM7Z0JBQU87Z0JBQVM7Z0JBQVU7YUFBVTtZQUM1Q0MsU0FBUztRQUNYO0tBQ0Q7SUFFRCw2QkFBNkI7SUFDN0JqQixnREFBU0EsQ0FBQztRQUNSLE1BQU1rQixvQkFBb0I7WUFDeEJYO1FBQ0Y7UUFFQUcsT0FBT1MsTUFBTSxDQUFDQyxFQUFFLENBQUMsb0JBQW9CRjtRQUNyQyxPQUFPO1lBQ0xSLE9BQU9TLE1BQU0sQ0FBQ0UsR0FBRyxDQUFDLG9CQUFvQkg7UUFDeEM7SUFDRixHQUFHO1FBQUNSLE9BQU9TLE1BQU07UUFBRVo7S0FBUTtJQUUzQiwyQkFBMkI7SUFDM0JQLGdEQUFTQSxDQUFDO1FBQ1IsTUFBTXNCLGVBQWUsQ0FBQ0M7WUFDcEIsSUFBSUEsRUFBRUMsR0FBRyxLQUFLLFlBQVlsQixRQUFRO2dCQUNoQ0M7WUFDRjtRQUNGO1FBRUFrQixTQUFTQyxnQkFBZ0IsQ0FBQyxXQUFXSjtRQUNyQyxPQUFPLElBQU1HLFNBQVNFLG1CQUFtQixDQUFDLFdBQVdMO0lBQ3ZELEdBQUc7UUFBQ2hCO1FBQVFDO0tBQVE7SUFFcEIsd0NBQXdDO0lBQ3hDUCxnREFBU0EsQ0FBQztRQUNSLElBQUlNLFFBQVE7WUFDVm1CLFNBQVNHLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7UUFDakMsT0FBTztZQUNMTCxTQUFTRyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxHQUFHO1FBQ2pDO1FBRUEsT0FBTztZQUNMTCxTQUFTRyxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxHQUFHO1FBQ2pDO0lBQ0YsR0FBRztRQUFDeEI7S0FBTztJQUVYLE1BQU15QixzQkFBc0IsQ0FBQ0M7UUFDM0IsSUFBSTtZQUNGQyxRQUFRQyxHQUFHLENBQUMsMENBQWdDRixLQUFLbkIsS0FBSyxFQUFFbUIsS0FBS2pCLElBQUk7WUFDakVaLHVFQUFjQSxDQUFDZ0MsS0FBSztZQUNwQjVCO1FBQ0YsRUFBRSxPQUFPNkIsT0FBTztZQUNkSCxRQUFRRyxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRDdCLFdBQVcsNENBQTRDO1FBQ3pEO0lBQ0Y7SUFFQSxNQUFNOEIsc0JBQXNCLENBQUNkO1FBQzNCLElBQUlBLEVBQUVlLE1BQU0sS0FBS2YsRUFBRWdCLGFBQWEsRUFBRTtZQUNoQ3BDLHVFQUFjQSxDQUFDZ0MsS0FBSztZQUNwQjVCO1FBQ0Y7SUFDRjtJQUVBLE1BQU1pQyxXQUFXLENBQUN6QjtRQUNoQixJQUFJQSxTQUFTLG9CQUFvQjtZQUMvQixPQUFPTCxPQUFPK0IsUUFBUSxLQUFLLHNCQUFzQi9CLE9BQU8rQixRQUFRLEtBQUs7UUFDdkU7UUFDQSxPQUFPL0IsT0FBTytCLFFBQVEsQ0FBQ0MsVUFBVSxDQUFDM0I7SUFDcEM7SUFFQSxNQUFNNEIsb0JBQW9CaEMsVUFBVWlDLE1BQU0sQ0FBQ1osQ0FBQUEsT0FDekNBLEtBQUtoQixLQUFLLENBQUM2QixRQUFRLENBQUNyQztJQUd0QixnQkFBZ0I7SUFDaEJ5QixRQUFRQyxHQUFHLENBQUMsbUNBQXlCO1FBQ25DMUI7UUFDQXNDLGdCQUFnQm5DLFVBQVVvQyxNQUFNO1FBQ2hDSixtQkFBbUJBLGtCQUFrQkksTUFBTTtRQUMzQ0MsZUFBZUwsa0JBQWtCTSxHQUFHLENBQUNqQixDQUFBQSxPQUFRQSxLQUFLbkIsS0FBSztJQUN6RDtJQUVBLE1BQU1xQyxlQUFlUCxrQkFBa0JRLE1BQU0sQ0FBQyxDQUFDQyxRQUFRcEI7UUFDckQsTUFBTWYsVUFBVWUsS0FBS2YsT0FBTyxJQUFJO1FBQ2hDLElBQUksQ0FBQ21DLE1BQU0sQ0FBQ25DLFFBQVEsRUFBRTtZQUNwQm1DLE1BQU0sQ0FBQ25DLFFBQVEsR0FBRyxFQUFFO1FBQ3RCO1FBQ0FtQyxNQUFNLENBQUNuQyxRQUFRLENBQUNvQyxJQUFJLENBQUNyQjtRQUNyQixPQUFPb0I7SUFDVCxHQUFHLENBQUM7SUFFSixJQUFJLENBQUM5QyxRQUFRLE9BQU87SUFFcEIsb0NBQW9DO0lBQ3BDLElBQUk7UUFDRixxQkFDRSw4REFBQ2dEO1lBQ0NDLFdBQVcsR0FBeUJqRCxPQUF0QkYsd0dBQWtCLEVBQUMsS0FBNkIsT0FBMUJFLFNBQVNGLGlHQUFXLEdBQUc7WUFDM0RzRCxTQUFTckI7c0JBRVgsNEVBQUNpQjtnQkFBSUMsV0FBV25ELDBHQUFvQjs7a0NBRWxDLDhEQUFDa0Q7d0JBQUlDLFdBQVduRCx1R0FBaUI7OzBDQUMvQiw4REFBQ2tEO2dDQUFJQyxXQUFXbkQscUdBQWU7O2tEQUM3Qiw4REFBQ2tEO3dDQUFJQyxXQUFXbkQsdUdBQWlCO2tEQUM5QkssU0FBU3NELE1BQU0sQ0FBQyxHQUFHQyxXQUFXOzs7Ozs7a0RBRWpDLDhEQUFDVjt3Q0FBSUMsV0FBV25ELHdHQUFrQjs7MERBQ2hDLDhEQUFDOEQ7Z0RBQUdYLFdBQVduRCxxR0FBZTswREFBR0s7Ozs7OzswREFDakMsOERBQUMwRDtnREFBS1osV0FBV25ELHFHQUFlOzBEQUFHSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUd2Qyw4REFBQzREO2dDQUNDYixXQUFXbkQsd0dBQWtCO2dDQUM3QnNELFNBQVNuRDtnQ0FDVCtELGNBQVc7MENBQ1o7Ozs7Ozs7Ozs7OztrQ0FNSCw4REFBQ2hCO3dCQUFJQyxXQUFXbkQsd0dBQWtCO2tDQUMvQm9FLE9BQU9DLE9BQU8sQ0FBQ3ZCLGNBQWNELEdBQUcsQ0FBQztnQ0FBQyxDQUFDaEMsU0FBU3lELE1BQU07aURBQ2pELDhEQUFDcEI7Z0NBQWtCQyxXQUFXbkQsd0dBQWtCOztrREFDOUMsOERBQUN3RTt3Q0FBR3JCLFdBQVduRCx5R0FBbUI7a0RBQUdhOzs7Ozs7a0RBQ3JDLDhEQUFDcUM7d0NBQUlDLFdBQVduRCx5R0FBbUI7a0RBQ2hDc0UsTUFBTXpCLEdBQUcsQ0FBQyxDQUFDakIscUJBQ1YsOERBQUMvQixrREFBSUE7Z0RBRUhjLE1BQU1pQixLQUFLakIsSUFBSTtnREFDZndDLFdBQVcsR0FBc0JmLE9BQW5CcEMscUdBQWUsRUFBQyxLQUE0QyxPQUF6Q29DLFNBQVNSLEtBQUtqQixJQUFJLElBQUlYLG1HQUFhLEdBQUc7Z0RBQ3ZFc0QsU0FBUyxJQUFNM0Isb0JBQW9CQzs7a0VBRW5DLDhEQUFDc0I7d0RBQUlDLFdBQVduRCx5R0FBbUI7a0VBQ2hDNEIsS0FBS2xCLElBQUk7Ozs7OztrRUFFWiw4REFBQ3FEO3dEQUFLWixXQUFXbkQsMEdBQW9CO2tFQUNsQzRCLEtBQUtuQixLQUFLOzs7Ozs7b0RBRVoyQixTQUFTUixLQUFLakIsSUFBSSxtQkFDakIsOERBQUN1Qzt3REFBSUMsV0FBV25ELDRHQUFzQjs7Ozs7OzsrQ0FabkM0QixLQUFLcEIsRUFBRTs7Ozs7Ozs7Ozs7K0JBTFZLOzs7Ozs7Ozs7OztrQ0EyQmQsOERBQUNxQzt3QkFBSUMsV0FBV25ELHVHQUFpQjtrQ0FDL0IsNEVBQUNrRDs0QkFBSUMsV0FBV25ELG9HQUFjOzs4Q0FDNUIsOERBQUMrRDtvQ0FBS1osV0FBV25ELG9HQUFjOzhDQUFFOzs7Ozs7OENBQ2pDLDhEQUFDK0Q7b0NBQUtaLFdBQVduRCx1R0FBaUI7OENBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNOUMsRUFBRSxPQUFPZ0MsT0FBTztRQUNkSCxRQUFRRyxLQUFLLENBQUMsNENBQTRDQTtRQUMxRCxxQkFDRSw4REFBQ2tCO1lBQUlDLFdBQVcsR0FBeUJqRCxPQUF0QkYsd0dBQWtCLEVBQUMsS0FBNkIsT0FBMUJFLFNBQVNGLGlHQUFXLEdBQUc7c0JBQzlELDRFQUFDa0Q7Z0JBQUlDLFdBQVduRCwwR0FBb0I7O2tDQUNsQyw4REFBQ2tEO3dCQUFJQyxXQUFXbkQsdUdBQWlCOzswQ0FDL0IsOERBQUNrRDtnQ0FBSUMsV0FBV25ELHFHQUFlOztrREFDN0IsOERBQUNrRDt3Q0FBSUMsV0FBV25ELHVHQUFpQjtrREFBRTs7Ozs7O2tEQUNuQyw4REFBQ2tEO3dDQUFJQyxXQUFXbkQsd0dBQWtCOzswREFDaEMsOERBQUM4RDtnREFBR1gsV0FBV25ELHFHQUFlOzBEQUFFOzs7Ozs7MERBQ2hDLDhEQUFDK0Q7Z0RBQUtaLFdBQVduRCxxR0FBZTswREFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUd0Qyw4REFBQ2dFO2dDQUFPYixXQUFXbkQsd0dBQWtCO2dDQUFFc0QsU0FBU25EOzBDQUFTOzs7Ozs7Ozs7Ozs7a0NBRTNELDhEQUFDK0M7d0JBQUlDLFdBQVduRCx3R0FBa0I7a0NBQ2hDLDRFQUFDa0Q7NEJBQUlDLFdBQVduRCx3R0FBa0I7OzhDQUNoQyw4REFBQ3dFO29DQUFHckIsV0FBV25ELHlHQUFtQjs4Q0FBRTs7Ozs7OzhDQUNwQyw4REFBQ2tEO29DQUFJQyxXQUFXbkQseUdBQW1COzhDQUNqQyw0RUFBQ2tEO3dDQUFJQyxXQUFXbkQscUdBQWU7OzBEQUM3Qiw4REFBQ2tEO2dEQUFJQyxXQUFXbkQseUdBQW1COzBEQUFFOzs7Ozs7MERBQ3JDLDhEQUFDK0Q7Z0RBQUtaLFdBQVduRCwwR0FBb0I7MERBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVF2RDtBQUNGO0dBeFN3QkM7O1FBTVBILGtEQUFTQTs7O0tBTkZHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2NvbXBvbmVudHMvYWRtaW4vbW9iaWxlL01vYmlsZUhhbWJ1cmdlck1lbnUudHN4PzZkMDgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBPY2VhbiBTb3VsIFNwYXJrbGVzIEFkbWluIC0gTW9iaWxlIEhhbWJ1cmdlciBNZW51IENvbXBvbmVudFxuICogU2xpZGUtb3V0IG1vYmlsZSBtZW51IGZvciBhZGRpdGlvbmFsIG5hdmlnYXRpb24gb3B0aW9uc1xuICovXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgSGFwdGljRmVlZGJhY2sgfSBmcm9tICcuLi8uLi8uLi9saWIvZ2VzdHVyZXMvc3dpcGUtaGFuZGxlcic7XG5pbXBvcnQgc3R5bGVzIGZyb20gJy4uLy4uLy4uL3N0eWxlcy9hZG1pbi9tb2JpbGUvTW9iaWxlSGFtYnVyZ2VyTWVudS5tb2R1bGUuY3NzJztcblxuaW50ZXJmYWNlIE1vYmlsZUhhbWJ1cmdlck1lbnVQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgdXNlclJvbGU6IHN0cmluZztcbiAgdXNlck5hbWU6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIE1lbnVJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgbGFiZWw6IHN0cmluZztcbiAgaWNvbjogc3RyaW5nO1xuICBocmVmOiBzdHJpbmc7XG4gIHJvbGVzOiBzdHJpbmdbXTtcbiAgc2VjdGlvbj86IHN0cmluZztcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTW9iaWxlSGFtYnVyZ2VyTWVudSh7XG4gIGlzT3BlbixcbiAgb25DbG9zZSxcbiAgdXNlclJvbGUsXG4gIHVzZXJOYW1lXG59OiBNb2JpbGVIYW1idXJnZXJNZW51UHJvcHMpIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgY29uc3QgbWVudUl0ZW1zOiBNZW51SXRlbVtdID0gW1xuICAgIC8vIENvcmUgRmVhdHVyZXNcbiAgICB7XG4gICAgICBpZDogJ2Rhc2hib2FyZCcsXG4gICAgICBsYWJlbDogJ0Rhc2hib2FyZCcsXG4gICAgICBpY29uOiAn8J+TiicsXG4gICAgICBocmVmOiAnL2FkbWluL2Rhc2hib2FyZCcsXG4gICAgICByb2xlczogWydERVYnLCAnQWRtaW4nLCAnQXJ0aXN0JywgJ0JyYWlkZXInXSxcbiAgICAgIHNlY3Rpb246ICdDb3JlJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwb3MnLFxuICAgICAgbGFiZWw6ICdQb2ludCBvZiBTYWxlJyxcbiAgICAgIGljb246ICfwn5KzJyxcbiAgICAgIGhyZWY6ICcvYWRtaW4vcG9zJyxcbiAgICAgIHJvbGVzOiBbJ0RFVicsICdBZG1pbicsICdBcnRpc3QnLCAnQnJhaWRlciddLFxuICAgICAgc2VjdGlvbjogJ0NvcmUnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ2Jvb2tpbmdzJyxcbiAgICAgIGxhYmVsOiAnQm9va2luZ3MnLFxuICAgICAgaWNvbjogJ/Cfk4UnLFxuICAgICAgaHJlZjogJy9hZG1pbi9ib29raW5ncycsXG4gICAgICByb2xlczogWydERVYnLCAnQWRtaW4nLCAnQXJ0aXN0JywgJ0JyYWlkZXInXSxcbiAgICAgIHNlY3Rpb246ICdDb3JlJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdjdXN0b21lcnMnLFxuICAgICAgbGFiZWw6ICdDdXN0b21lcnMnLFxuICAgICAgaWNvbjogJ/CfkaUnLFxuICAgICAgaHJlZjogJy9hZG1pbi9jdXN0b21lcnMnLFxuICAgICAgcm9sZXM6IFsnREVWJywgJ0FkbWluJ10sXG4gICAgICBzZWN0aW9uOiAnQ29yZSdcbiAgICB9LFxuICAgIFxuICAgIC8vIE1hbmFnZW1lbnRcbiAgICB7XG4gICAgICBpZDogJ3NlcnZpY2VzJyxcbiAgICAgIGxhYmVsOiAnU2VydmljZXMnLFxuICAgICAgaWNvbjogJ+Kcgu+4jycsXG4gICAgICBocmVmOiAnL2FkbWluL3NlcnZpY2VzJyxcbiAgICAgIHJvbGVzOiBbJ0RFVicsICdBZG1pbiddLFxuICAgICAgc2VjdGlvbjogJ01hbmFnZW1lbnQnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJ3Byb2R1Y3RzJyxcbiAgICAgIGxhYmVsOiAnUHJvZHVjdHMnLFxuICAgICAgaWNvbjogJ/Cfm43vuI8nLFxuICAgICAgaHJlZjogJy9hZG1pbi9wcm9kdWN0cycsXG4gICAgICByb2xlczogWydERVYnLCAnQWRtaW4nXSxcbiAgICAgIHNlY3Rpb246ICdNYW5hZ2VtZW50J1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdpbnZlbnRvcnknLFxuICAgICAgbGFiZWw6ICdJbnZlbnRvcnknLFxuICAgICAgaWNvbjogJ/Cfk6YnLFxuICAgICAgaHJlZjogJy9hZG1pbi9pbnZlbnRvcnknLFxuICAgICAgcm9sZXM6IFsnREVWJywgJ0FkbWluJ10sXG4gICAgICBzZWN0aW9uOiAnTWFuYWdlbWVudCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnc3RhZmYnLFxuICAgICAgbGFiZWw6ICdTdGFmZiBNYW5hZ2VtZW50JyxcbiAgICAgIGljb246ICfwn5Go4oCN8J+SvCcsXG4gICAgICBocmVmOiAnL2FkbWluL3N0YWZmJyxcbiAgICAgIHJvbGVzOiBbJ0RFVicsICdBZG1pbiddLFxuICAgICAgc2VjdGlvbjogJ01hbmFnZW1lbnQnXG4gICAgfSxcbiAgICBcbiAgICAvLyBBbmFseXRpY3MgJiBSZXBvcnRzXG4gICAge1xuICAgICAgaWQ6ICdhbmFseXRpY3MnLFxuICAgICAgbGFiZWw6ICdBbmFseXRpY3MnLFxuICAgICAgaWNvbjogJ/Cfk4gnLFxuICAgICAgaHJlZjogJy9hZG1pbi9hbmFseXRpY3MnLFxuICAgICAgcm9sZXM6IFsnREVWJywgJ0FkbWluJ10sXG4gICAgICBzZWN0aW9uOiAnQW5hbHl0aWNzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdyZXBvcnRzJyxcbiAgICAgIGxhYmVsOiAnUmVwb3J0cycsXG4gICAgICBpY29uOiAn8J+TiycsXG4gICAgICBocmVmOiAnL2FkbWluL3JlcG9ydHMnLFxuICAgICAgcm9sZXM6IFsnREVWJywgJ0FkbWluJ10sXG4gICAgICBzZWN0aW9uOiAnQW5hbHl0aWNzJ1xuICAgIH0sXG4gICAgXG4gICAgLy8gU2V0dGluZ3NcbiAgICB7XG4gICAgICBpZDogJ3NldHRpbmdzJyxcbiAgICAgIGxhYmVsOiAnU2V0dGluZ3MnLFxuICAgICAgaWNvbjogJ+Kame+4jycsXG4gICAgICBocmVmOiAnL2FkbWluL3NldHRpbmdzJyxcbiAgICAgIHJvbGVzOiBbJ0RFVicsICdBZG1pbiddLFxuICAgICAgc2VjdGlvbjogJ1NldHRpbmdzJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICdwcm9maWxlJyxcbiAgICAgIGxhYmVsOiAnUHJvZmlsZScsXG4gICAgICBpY29uOiAn8J+RpCcsXG4gICAgICBocmVmOiAnL2FkbWluL3Byb2ZpbGUnLFxuICAgICAgcm9sZXM6IFsnREVWJywgJ0FkbWluJywgJ0FydGlzdCcsICdCcmFpZGVyJ10sXG4gICAgICBzZWN0aW9uOiAnU2V0dGluZ3MnXG4gICAgfVxuICBdO1xuXG4gIC8vIENsb3NlIG1lbnUgb24gcm91dGUgY2hhbmdlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlUm91dGVDaGFuZ2UgPSAoKSA9PiB7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfTtcblxuICAgIHJvdXRlci5ldmVudHMub24oJ3JvdXRlQ2hhbmdlU3RhcnQnLCBoYW5kbGVSb3V0ZUNoYW5nZSk7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHJvdXRlci5ldmVudHMub2ZmKCdyb3V0ZUNoYW5nZVN0YXJ0JywgaGFuZGxlUm91dGVDaGFuZ2UpO1xuICAgIH07XG4gIH0sIFtyb3V0ZXIuZXZlbnRzLCBvbkNsb3NlXSk7XG5cbiAgLy8gQ2xvc2UgbWVudSBvbiBlc2NhcGUga2V5XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgaGFuZGxlRXNjYXBlID0gKGU6IEtleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgIGlmIChlLmtleSA9PT0gJ0VzY2FwZScgJiYgaXNPcGVuKSB7XG4gICAgICAgIG9uQ2xvc2UoKTtcbiAgICAgIH1cbiAgICB9O1xuXG4gICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUVzY2FwZSk7XG4gICAgcmV0dXJuICgpID0+IGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NhcGUpO1xuICB9LCBbaXNPcGVuLCBvbkNsb3NlXSk7XG5cbiAgLy8gUHJldmVudCBib2R5IHNjcm9sbCB3aGVuIG1lbnUgaXMgb3BlblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChpc09wZW4pIHtcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAnaGlkZGVuJztcbiAgICB9IGVsc2Uge1xuICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS5vdmVyZmxvdyA9ICcnO1xuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJyc7XG4gICAgfTtcbiAgfSwgW2lzT3Blbl0pO1xuXG4gIGNvbnN0IGhhbmRsZU1lbnVJdGVtQ2xpY2sgPSAoaXRlbTogTWVudUl0ZW0pID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ/CflJcgTW9iaWxlIG1lbnUgaXRlbSBjbGlja2VkOicsIGl0ZW0ubGFiZWwsIGl0ZW0uaHJlZik7XG4gICAgICBIYXB0aWNGZWVkYmFjay5saWdodCgpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgaW4gbW9iaWxlIG1lbnUgaXRlbSBjbGljazonLCBlcnJvcik7XG4gICAgICBvbkNsb3NlKCk7IC8vIFN0aWxsIGNsb3NlIG1lbnUgZXZlbiBpZiB0aGVyZSdzIGFuIGVycm9yXG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUJhY2tkcm9wQ2xpY2sgPSAoZTogUmVhY3QuTW91c2VFdmVudCkgPT4ge1xuICAgIGlmIChlLnRhcmdldCA9PT0gZS5jdXJyZW50VGFyZ2V0KSB7XG4gICAgICBIYXB0aWNGZWVkYmFjay5saWdodCgpO1xuICAgICAgb25DbG9zZSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpc0FjdGl2ZSA9IChocmVmOiBzdHJpbmcpOiBib29sZWFuID0+IHtcbiAgICBpZiAoaHJlZiA9PT0gJy9hZG1pbi9kYXNoYm9hcmQnKSB7XG4gICAgICByZXR1cm4gcm91dGVyLnBhdGhuYW1lID09PSAnL2FkbWluL2Rhc2hib2FyZCcgfHwgcm91dGVyLnBhdGhuYW1lID09PSAnL2FkbWluJztcbiAgICB9XG4gICAgcmV0dXJuIHJvdXRlci5wYXRobmFtZS5zdGFydHNXaXRoKGhyZWYpO1xuICB9O1xuXG4gIGNvbnN0IGZpbHRlcmVkTWVudUl0ZW1zID0gbWVudUl0ZW1zLmZpbHRlcihpdGVtID0+XG4gICAgaXRlbS5yb2xlcy5pbmNsdWRlcyh1c2VyUm9sZSlcbiAgKTtcblxuICAvLyBEZWJ1ZyBsb2dnaW5nXG4gIGNvbnNvbGUubG9nKCfwn5SNIE1vYmlsZSBNZW51IERlYnVnOicsIHtcbiAgICB1c2VyUm9sZSxcbiAgICB0b3RhbE1lbnVJdGVtczogbWVudUl0ZW1zLmxlbmd0aCxcbiAgICBmaWx0ZXJlZE1lbnVJdGVtczogZmlsdGVyZWRNZW51SXRlbXMubGVuZ3RoLFxuICAgIGZpbHRlcmVkSXRlbXM6IGZpbHRlcmVkTWVudUl0ZW1zLm1hcChpdGVtID0+IGl0ZW0ubGFiZWwpXG4gIH0pO1xuXG4gIGNvbnN0IGdyb3VwZWRJdGVtcyA9IGZpbHRlcmVkTWVudUl0ZW1zLnJlZHVjZSgoZ3JvdXBzLCBpdGVtKSA9PiB7XG4gICAgY29uc3Qgc2VjdGlvbiA9IGl0ZW0uc2VjdGlvbiB8fCAnT3RoZXInO1xuICAgIGlmICghZ3JvdXBzW3NlY3Rpb25dKSB7XG4gICAgICBncm91cHNbc2VjdGlvbl0gPSBbXTtcbiAgICB9XG4gICAgZ3JvdXBzW3NlY3Rpb25dLnB1c2goaXRlbSk7XG4gICAgcmV0dXJuIGdyb3VwcztcbiAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgTWVudUl0ZW1bXT4pO1xuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICAvLyBFcnJvciBib3VuZGFyeSBmb3IgbWVudSByZW5kZXJpbmdcbiAgdHJ5IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdlxuICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5tZW51T3ZlcmxheX0gJHtpc09wZW4gPyBzdHlsZXMub3BlbiA6ICcnfWB9XG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZUJhY2tkcm9wQ2xpY2t9XG4gICAgICA+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVDb250YWluZXJ9PlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVIZWFkZXJ9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMudXNlckluZm99PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy51c2VyQXZhdGFyfT5cbiAgICAgICAgICAgICAge3VzZXJOYW1lLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJEZXRhaWxzfT5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT17c3R5bGVzLnVzZXJOYW1lfT57dXNlck5hbWV9PC9oMz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMudXNlclJvbGV9Pnt1c2VyUm9sZX08L3NwYW4+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBjbGFzc05hbWU9e3N0eWxlcy5jbG9zZUJ1dHRvbn1cbiAgICAgICAgICAgIG9uQ2xpY2s9e29uQ2xvc2V9XG4gICAgICAgICAgICBhcmlhLWxhYmVsPVwiQ2xvc2UgbWVudVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAg4pyVXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBNZW51IENvbnRlbnQgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubWVudUNvbnRlbnR9PlxuICAgICAgICAgIHtPYmplY3QuZW50cmllcyhncm91cGVkSXRlbXMpLm1hcCgoW3NlY3Rpb24sIGl0ZW1zXSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e3NlY3Rpb259IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVTZWN0aW9ufT5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17c3R5bGVzLnNlY3Rpb25UaXRsZX0+e3NlY3Rpb259PC9oND5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5zZWN0aW9uSXRlbXN9PlxuICAgICAgICAgICAgICAgIHtpdGVtcy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5pZH1cbiAgICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Ake3N0eWxlcy5tZW51SXRlbX0gJHtpc0FjdGl2ZShpdGVtLmhyZWYpID8gc3R5bGVzLmFjdGl2ZSA6ICcnfWB9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU1lbnVJdGVtQ2xpY2soaXRlbSl9XG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubWVudUl0ZW1JY29ufT5cbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS5pY29ufVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMubWVudUl0ZW1MYWJlbH0+XG4gICAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAge2lzQWN0aXZlKGl0ZW0uaHJlZikgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuYWN0aXZlSW5kaWNhdG9yfSAvPlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRm9vdGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVGb290ZXJ9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMuYXBwSW5mb30+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5hcHBOYW1lfT5PY2VhbiBTb3VsIFNwYXJrbGVzIEFkbWluPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMuYXBwVmVyc2lvbn0+djEuMC4wPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciByZW5kZXJpbmcgbW9iaWxlIGhhbWJ1cmdlciBtZW51OicsIGVycm9yKTtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Ake3N0eWxlcy5tZW51T3ZlcmxheX0gJHtpc09wZW4gPyBzdHlsZXMub3BlbiA6ICcnfWB9PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVDb250YWluZXJ9PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMubWVudUhlYWRlcn0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnVzZXJJbmZvfT5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy51c2VyQXZhdGFyfT4hPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtzdHlsZXMudXNlckRldGFpbHN9PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9e3N0eWxlcy51c2VyTmFtZX0+RXJyb3I8L2gzPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17c3R5bGVzLnVzZXJSb2xlfT5NZW51IEVycm9yPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9e3N0eWxlcy5jbG9zZUJ1dHRvbn0gb25DbGljaz17b25DbG9zZX0+4pyVPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5tZW51Q29udGVudH0+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVTZWN0aW9ufT5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT17c3R5bGVzLnNlY3Rpb25UaXRsZX0+RXJyb3I8L2g0PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnNlY3Rpb25JdGVtc30+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5tZW51SXRlbX0+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLm1lbnVJdGVtSWNvbn0+4p2MPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e3N0eWxlcy5tZW51SXRlbUxhYmVsfT5NZW51IGZhaWxlZCB0byBsb2FkPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsIkxpbmsiLCJ1c2VSb3V0ZXIiLCJIYXB0aWNGZWVkYmFjayIsInN0eWxlcyIsIk1vYmlsZUhhbWJ1cmdlck1lbnUiLCJpc09wZW4iLCJvbkNsb3NlIiwidXNlclJvbGUiLCJ1c2VyTmFtZSIsInJvdXRlciIsIm1lbnVJdGVtcyIsImlkIiwibGFiZWwiLCJpY29uIiwiaHJlZiIsInJvbGVzIiwic2VjdGlvbiIsImhhbmRsZVJvdXRlQ2hhbmdlIiwiZXZlbnRzIiwib24iLCJvZmYiLCJoYW5kbGVFc2NhcGUiLCJlIiwia2V5IiwiZG9jdW1lbnQiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImJvZHkiLCJzdHlsZSIsIm92ZXJmbG93IiwiaGFuZGxlTWVudUl0ZW1DbGljayIsIml0ZW0iLCJjb25zb2xlIiwibG9nIiwibGlnaHQiLCJlcnJvciIsImhhbmRsZUJhY2tkcm9wQ2xpY2siLCJ0YXJnZXQiLCJjdXJyZW50VGFyZ2V0IiwiaXNBY3RpdmUiLCJwYXRobmFtZSIsInN0YXJ0c1dpdGgiLCJmaWx0ZXJlZE1lbnVJdGVtcyIsImZpbHRlciIsImluY2x1ZGVzIiwidG90YWxNZW51SXRlbXMiLCJsZW5ndGgiLCJmaWx0ZXJlZEl0ZW1zIiwibWFwIiwiZ3JvdXBlZEl0ZW1zIiwicmVkdWNlIiwiZ3JvdXBzIiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSIsIm1lbnVPdmVybGF5Iiwib3BlbiIsIm9uQ2xpY2siLCJtZW51Q29udGFpbmVyIiwibWVudUhlYWRlciIsInVzZXJJbmZvIiwidXNlckF2YXRhciIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwidXNlckRldGFpbHMiLCJoMyIsInNwYW4iLCJidXR0b24iLCJjbG9zZUJ1dHRvbiIsImFyaWEtbGFiZWwiLCJtZW51Q29udGVudCIsIk9iamVjdCIsImVudHJpZXMiLCJpdGVtcyIsIm1lbnVTZWN0aW9uIiwiaDQiLCJzZWN0aW9uVGl0bGUiLCJzZWN0aW9uSXRlbXMiLCJtZW51SXRlbSIsImFjdGl2ZSIsIm1lbnVJdGVtSWNvbiIsIm1lbnVJdGVtTGFiZWwiLCJhY3RpdmVJbmRpY2F0b3IiLCJtZW51Rm9vdGVyIiwiYXBwSW5mbyIsImFwcE5hbWUiLCJhcHBWZXJzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./components/admin/mobile/MobileHamburgerMenu.tsx\n"));

/***/ })

});