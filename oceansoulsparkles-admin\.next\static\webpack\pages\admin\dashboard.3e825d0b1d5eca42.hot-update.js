"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/admin/dashboard",{

/***/ "./components/admin/AdminLayout.tsx":
/*!******************************************!*\
  !*** ./components/admin/AdminLayout.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _AdminSidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./AdminSidebar */ \"./components/admin/AdminSidebar.tsx\");\n/* harmony import */ var _AdminHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./AdminHeader */ \"./components/admin/AdminHeader.tsx\");\n/* harmony import */ var _mobile_MobileBottomNav__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./mobile/MobileBottomNav */ \"./components/admin/mobile/MobileBottomNav.tsx\");\n/* harmony import */ var _PWAManager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./PWAManager */ \"./components/admin/PWAManager.tsx\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../hooks/useAuth */ \"./hooks/useAuth.ts\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../styles/admin/AdminLayout.module.css */ \"./styles/admin/AdminLayout.module.css\");\n/* harmony import */ var _styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// import MobileHamburgerMenu from './mobile/MobileHamburgerMenu';\n\n\n\nfunction AdminLayout(param) {\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__.useAuth)();\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if mobile\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth <= 1024);\n            if (window.innerWidth <= 1024) {\n                setSidebarCollapsed(true);\n            }\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Redirect to login if not authenticated\n        if (!loading && !user) {\n            router.push(\"/admin/login\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    const handleLogout = async ()=>{\n        try {\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"admin-token\"))\n                }\n            });\n            if (response.ok) {\n                localStorage.removeItem(\"admin-token\");\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Logged out successfully\");\n                router.push(\"/admin/login\");\n            } else {\n                throw new Error(\"Logout failed\");\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n            // Force logout even if API call fails\n            localStorage.removeItem(\"admin-token\");\n            router.push(\"/admin/login\");\n        }\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        console.log(\"\\uD83D\\uDCF1 Mobile menu toggle:\", {\n            current: mobileMenuOpen,\n            willBe: !mobileMenuOpen,\n            isMobile\n        });\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().loadingContainer),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().loadingSpinner)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading admin portal...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PWAManager__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().adminLayout),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminSidebar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    user: user,\n                    collapsed: sidebarCollapsed,\n                    onToggle: toggleSidebar,\n                    isMobile: isMobile\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat((_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().mainContent), \" \").concat(sidebarCollapsed ? (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().sidebarCollapsed) : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AdminHeader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            user: user,\n                            onLogout: handleLogout,\n                            onToggleSidebar: isMobile ? toggleMobileMenu : toggleSidebar,\n                            sidebarCollapsed: sidebarCollapsed\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().pageContent),\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().adminFooter),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerContent),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerLeft),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"\\xa9 2024 Ocean Soul Sparkles Admin Portal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().version),\n                                                children: \"v1.0.0\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerRight),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/help\",\n                                                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerLink),\n                                                children: \"Help\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/privacy\",\n                                                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerLink),\n                                                children: \"Privacy\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/admin/terms\",\n                                                className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().footerLink),\n                                                children: \"Terms\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, this),\n                isMobile && mobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().mobileOverlay),\n                    onClick: ()=>setMobileMenuOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().mobileBottomNav),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mobile_MobileBottomNav__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        userRole: (user === null || user === void 0 ? void 0 : user.role) || \"Admin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().debugMobile),\n                    children: [\n                        \"Mobile: \",\n                        isMobile ? \"YES\" : \"NO\",\n                        \" | Width: \",\n                         true ? window.innerWidth : 0\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().securityBanner),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (_styles_admin_AdminLayout_module_css__WEBPACK_IMPORTED_MODULE_10___default().securityIcon),\n                            children: \"\\uD83D\\uDD12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Secure Admin Portal - All actions are logged and monitored\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 7\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n            lineNumber: 97,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\connectplusapps website\\\\admin.oceansoulsparkles.com.au\\\\oceansoulsparkles-admin\\\\components\\\\admin\\\\AdminLayout.tsx\",\n        lineNumber: 96,\n        columnNumber: 5\n    }, this);\n}\n_s(AdminLayout, \"BtI9TB7ddU+7Ea/+M6o1Pnui6rc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_9__.useAuth\n    ];\n});\n_c = AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9jb21wb25lbnRzL2FkbWluL0FkbWluTGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFDWDtBQUNVO0FBQ0c7QUFDRjtBQUNlO0FBQ3ZELGtFQUFrRTtBQUM1QjtBQUNRO0FBQ2lCO0FBTWhELFNBQVNXLFlBQVksS0FBOEI7UUFBOUIsRUFBRUMsUUFBUSxFQUFvQixHQUE5Qjs7SUFDbEMsTUFBTUMsU0FBU1gsc0RBQVNBO0lBQ3hCLE1BQU0sRUFBRVksSUFBSSxFQUFFQyxPQUFPLEVBQUUsR0FBR04sdURBQU9BO0lBQ2pDLE1BQU0sQ0FBQ08sa0JBQWtCQyxvQkFBb0IsR0FBR2pCLCtDQUFRQSxDQUFDO0lBQ3pELE1BQU0sQ0FBQ2tCLFVBQVVDLFlBQVksR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQ29CLGdCQUFnQkMsa0JBQWtCLEdBQUdyQiwrQ0FBUUEsQ0FBQztJQUVyREMsZ0RBQVNBLENBQUM7UUFDUixrQkFBa0I7UUFDbEIsTUFBTXFCLGNBQWM7WUFDbEJILFlBQVlJLE9BQU9DLFVBQVUsSUFBSTtZQUNqQyxJQUFJRCxPQUFPQyxVQUFVLElBQUksTUFBTTtnQkFDN0JQLG9CQUFvQjtZQUN0QjtRQUNGO1FBRUFLO1FBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1FBQ2xDLE9BQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7SUFDcEQsR0FBRyxFQUFFO0lBRUxyQixnREFBU0EsQ0FBQztRQUNSLHlDQUF5QztRQUN6QyxJQUFJLENBQUNjLFdBQVcsQ0FBQ0QsTUFBTTtZQUNyQkQsT0FBT2MsSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNiO1FBQU1DO1FBQVNGO0tBQU87SUFFMUIsTUFBTWUsZUFBZTtRQUNuQixJQUFJO1lBQ0YsTUFBTUMsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtnQkFDL0NDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsaUJBQWlCLFVBQThDLE9BQXBDQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ2xEO1lBQ0Y7WUFFQSxJQUFJTCxTQUFTTSxFQUFFLEVBQUU7Z0JBQ2ZGLGFBQWFHLFVBQVUsQ0FBQztnQkFDeEJoQyxpREFBS0EsQ0FBQ2lDLE9BQU8sQ0FBQztnQkFDZHhCLE9BQU9jLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0wsTUFBTSxJQUFJVyxNQUFNO1lBQ2xCO1FBQ0YsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQy9CLHNDQUFzQztZQUN0Q04sYUFBYUcsVUFBVSxDQUFDO1lBQ3hCdkIsT0FBT2MsSUFBSSxDQUFDO1FBQ2Q7SUFDRjtJQUVBLE1BQU1jLGdCQUFnQjtRQUNwQnhCLG9CQUFvQixDQUFDRDtJQUN2QjtJQUVBLE1BQU0wQixtQkFBbUI7UUFDdkJGLFFBQVFHLEdBQUcsQ0FBQyxvQ0FBMEI7WUFDcENDLFNBQVN4QjtZQUNUeUIsUUFBUSxDQUFDekI7WUFDVEY7UUFDRjtRQUNBRyxrQkFBa0IsQ0FBQ0Q7SUFDckI7SUFFQSxJQUFJTCxTQUFTO1FBQ1gscUJBQ0UsOERBQUMrQjtZQUFJQyxXQUFXckMsK0ZBQXVCOzs4QkFDckMsOERBQUNvQztvQkFBSUMsV0FBV3JDLDZGQUFxQjs7Ozs7OzhCQUNyQyw4REFBQ3dDOzhCQUFFOzs7Ozs7Ozs7Ozs7SUFHVDtJQUVBLElBQUksQ0FBQ3BDLE1BQU07UUFDVCxPQUFPLE1BQU0seUJBQXlCO0lBQ3hDO0lBRUEscUJBQ0UsOERBQUNOLG1EQUFVQTtrQkFDVCw0RUFBQ3NDO1lBQUlDLFdBQVdyQywwRkFBa0I7OzhCQUVsQyw4REFBQ0wscURBQVlBO29CQUNYUyxNQUFNQTtvQkFDTnNDLFdBQVdwQztvQkFDWHFDLFVBQVVaO29CQUNWdkIsVUFBVUE7Ozs7Ozs4QkFJWiw4REFBQzRCO29CQUFJQyxXQUFXLEdBQXlCL0IsT0FBdEJOLDBGQUFrQixFQUFDLEtBQW1ELE9BQWhETSxtQkFBbUJOLCtGQUF1QixHQUFHOztzQ0FFcEYsOERBQUNKLG9EQUFXQTs0QkFDVlEsTUFBTUE7NEJBQ055QyxVQUFVM0I7NEJBQ1Y0QixpQkFBaUJ0QyxXQUFXd0IsbUJBQW1CRDs0QkFDL0N6QixrQkFBa0JBOzs7Ozs7c0NBSXBCLDhEQUFDeUM7NEJBQUtWLFdBQVdyQywwRkFBa0I7c0NBQ2hDRTs7Ozs7O3NDQUlILDhEQUFDK0M7NEJBQU9aLFdBQVdyQywwRkFBa0I7c0NBQ25DLDRFQUFDb0M7Z0NBQUlDLFdBQVdyQyw0RkFBb0I7O2tEQUNsQyw4REFBQ29DO3dDQUFJQyxXQUFXckMseUZBQWlCOzswREFDL0IsOERBQUNxRDswREFBSzs7Ozs7OzBEQUNOLDhEQUFDQTtnREFBS2hCLFdBQVdyQyxzRkFBYzswREFBRTs7Ozs7Ozs7Ozs7O2tEQUVuQyw4REFBQ29DO3dDQUFJQyxXQUFXckMsMEZBQWtCOzswREFDaEMsOERBQUNQLGtEQUFJQTtnREFBQytELE1BQUs7Z0RBQWNuQixXQUFXckMseUZBQWlCOzBEQUFFOzs7Ozs7MERBR3ZELDhEQUFDUCxrREFBSUE7Z0RBQUMrRCxNQUFLO2dEQUFpQm5CLFdBQVdyQyx5RkFBaUI7MERBQUU7Ozs7OzswREFHMUQsOERBQUNQLGtEQUFJQTtnREFBQytELE1BQUs7Z0RBQWVuQixXQUFXckMseUZBQWlCOzBEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQkFTL0RRLFlBQVlFLGdDQUNYLDhEQUFDMEI7b0JBQ0NDLFdBQVdyQyw0RkFBb0I7b0JBQy9CMkQsU0FBUyxJQUFNaEQsa0JBQWtCOzs7Ozs7OEJBS3JDLDhEQUFDeUI7b0JBQUlDLFdBQVdyQyw4RkFBc0I7OEJBQ3BDLDRFQUFDSCwrREFBZUE7d0JBQUNnRSxVQUFVekQsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNMEQsSUFBSSxLQUFJOzs7Ozs7Ozs7Ozs4QkFZM0MsOERBQUMxQjtvQkFBSUMsV0FBV3JDLDBGQUFrQjs7d0JBQUU7d0JBQ3pCUSxXQUFXLFFBQVE7d0JBQUs7d0JBQVcsS0FBa0IsR0FBY0ssT0FBT0MsVUFBVSxHQUFHOzs7Ozs7OzhCQUlsRyw4REFBQ3NCO29CQUFJQyxXQUFXckMsNkZBQXFCOztzQ0FDbkMsOERBQUNvQzs0QkFBSUMsV0FBV3JDLDJGQUFtQjtzQ0FBRTs7Ozs7O3NDQUNyQyw4REFBQ3FEO3NDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtkO0dBaEt3QnBEOztRQUNQVCxrREFBU0E7UUFDRU8sbURBQU9BOzs7S0FGWEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy9hZG1pbi9BZG1pbkxheW91dC50c3g/MzQ1OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XHJcbmltcG9ydCBMaW5rIGZyb20gJ25leHQvbGluayc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAncmVhY3QtdG9hc3RpZnknO1xyXG5pbXBvcnQgQWRtaW5TaWRlYmFyIGZyb20gJy4vQWRtaW5TaWRlYmFyJztcclxuaW1wb3J0IEFkbWluSGVhZGVyIGZyb20gJy4vQWRtaW5IZWFkZXInO1xyXG5pbXBvcnQgTW9iaWxlQm90dG9tTmF2IGZyb20gJy4vbW9iaWxlL01vYmlsZUJvdHRvbU5hdic7XHJcbi8vIGltcG9ydCBNb2JpbGVIYW1idXJnZXJNZW51IGZyb20gJy4vbW9iaWxlL01vYmlsZUhhbWJ1cmdlck1lbnUnO1xyXG5pbXBvcnQgUFdBTWFuYWdlciBmcm9tICcuL1BXQU1hbmFnZXInO1xyXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnLi4vLi4vaG9va3MvdXNlQXV0aCc7XHJcbmltcG9ydCBzdHlsZXMgZnJvbSAnLi4vLi4vc3R5bGVzL2FkbWluL0FkbWluTGF5b3V0Lm1vZHVsZS5jc3MnO1xyXG5cclxuaW50ZXJmYWNlIEFkbWluTGF5b3V0UHJvcHMge1xyXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkbWluTGF5b3V0KHsgY2hpbGRyZW4gfTogQWRtaW5MYXlvdXRQcm9wcykge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IHsgdXNlciwgbG9hZGluZyB9ID0gdXNlQXV0aCgpO1xyXG4gIGNvbnN0IFtzaWRlYmFyQ29sbGFwc2VkLCBzZXRTaWRlYmFyQ29sbGFwc2VkXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbbW9iaWxlTWVudU9wZW4sIHNldE1vYmlsZU1lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIENoZWNrIGlmIG1vYmlsZVxyXG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XHJcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDw9IDEwMjQpO1xyXG4gICAgICBpZiAod2luZG93LmlubmVyV2lkdGggPD0gMTAyNCkge1xyXG4gICAgICAgIHNldFNpZGViYXJDb2xsYXBzZWQodHJ1ZSk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY2hlY2tNb2JpbGUoKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAvLyBSZWRpcmVjdCB0byBsb2dpbiBpZiBub3QgYXV0aGVudGljYXRlZFxyXG4gICAgaWYgKCFsb2FkaW5nICYmICF1c2VyKSB7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vbG9naW4nKTtcclxuICAgIH1cclxuICB9LCBbdXNlciwgbG9hZGluZywgcm91dGVyXSk7XHJcblxyXG4gIGNvbnN0IGhhbmRsZUxvZ291dCA9IGFzeW5jICgpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dvdXQnLCB7XHJcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXHJcbiAgICAgICAgaGVhZGVyczoge1xyXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FkbWluLXRva2VuJyl9YFxyXG4gICAgICAgIH1cclxuICAgICAgfSk7XHJcblxyXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcclxuICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWRtaW4tdG9rZW4nKTtcclxuICAgICAgICB0b2FzdC5zdWNjZXNzKCdMb2dnZWQgb3V0IHN1Y2Nlc3NmdWxseScpO1xyXG4gICAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vbG9naW4nKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0xvZ291dCBmYWlsZWQnKTtcclxuICAgICAgfVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignTG9nb3V0IGVycm9yOicsIGVycm9yKTtcclxuICAgICAgLy8gRm9yY2UgbG9nb3V0IGV2ZW4gaWYgQVBJIGNhbGwgZmFpbHNcclxuICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oJ2FkbWluLXRva2VuJyk7XHJcbiAgICAgIHJvdXRlci5wdXNoKCcvYWRtaW4vbG9naW4nKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB0b2dnbGVTaWRlYmFyID0gKCkgPT4ge1xyXG4gICAgc2V0U2lkZWJhckNvbGxhcHNlZCghc2lkZWJhckNvbGxhcHNlZCk7XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdG9nZ2xlTW9iaWxlTWVudSA9ICgpID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCfwn5OxIE1vYmlsZSBtZW51IHRvZ2dsZTonLCB7XHJcbiAgICAgIGN1cnJlbnQ6IG1vYmlsZU1lbnVPcGVuLFxyXG4gICAgICB3aWxsQmU6ICFtb2JpbGVNZW51T3BlbixcclxuICAgICAgaXNNb2JpbGVcclxuICAgIH0pO1xyXG4gICAgc2V0TW9iaWxlTWVudU9wZW4oIW1vYmlsZU1lbnVPcGVuKTtcclxuICB9O1xyXG5cclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5sb2FkaW5nQ29udGFpbmVyfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmxvYWRpbmdTcGlubmVyfT48L2Rpdj5cclxuICAgICAgICA8cD5Mb2FkaW5nIGFkbWluIHBvcnRhbC4uLjwvcD5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKCF1c2VyKSB7XHJcbiAgICByZXR1cm4gbnVsbDsgLy8gV2lsbCByZWRpcmVjdCB0byBsb2dpblxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxQV0FNYW5hZ2VyPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmFkbWluTGF5b3V0fT5cclxuICAgICAgey8qIFNpZGViYXIgKi99XHJcbiAgICAgIDxBZG1pblNpZGViYXJcclxuICAgICAgICB1c2VyPXt1c2VyfVxyXG4gICAgICAgIGNvbGxhcHNlZD17c2lkZWJhckNvbGxhcHNlZH1cclxuICAgICAgICBvblRvZ2dsZT17dG9nZ2xlU2lkZWJhcn1cclxuICAgICAgICBpc01vYmlsZT17aXNNb2JpbGV9XHJcbiAgICAgIC8+XHJcblxyXG4gICAgICB7LyogTWFpbiBDb250ZW50IEFyZWEgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgJHtzdHlsZXMubWFpbkNvbnRlbnR9ICR7c2lkZWJhckNvbGxhcHNlZCA/IHN0eWxlcy5zaWRlYmFyQ29sbGFwc2VkIDogJyd9YH0+XHJcbiAgICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgICA8QWRtaW5IZWFkZXJcclxuICAgICAgICAgIHVzZXI9e3VzZXJ9XHJcbiAgICAgICAgICBvbkxvZ291dD17aGFuZGxlTG9nb3V0fVxyXG4gICAgICAgICAgb25Ub2dnbGVTaWRlYmFyPXtpc01vYmlsZSA/IHRvZ2dsZU1vYmlsZU1lbnUgOiB0b2dnbGVTaWRlYmFyfVxyXG4gICAgICAgICAgc2lkZWJhckNvbGxhcHNlZD17c2lkZWJhckNvbGxhcHNlZH1cclxuICAgICAgICAvPlxyXG5cclxuICAgICAgICB7LyogUGFnZSBDb250ZW50ICovfVxyXG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT17c3R5bGVzLnBhZ2VDb250ZW50fT5cclxuICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICA8L21haW4+XHJcblxyXG4gICAgICAgIHsvKiBGb290ZXIgKi99XHJcbiAgICAgICAgPGZvb3RlciBjbGFzc05hbWU9e3N0eWxlcy5hZG1pbkZvb3Rlcn0+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvb3RlckNvbnRlbnR9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmZvb3RlckxlZnR9PlxyXG4gICAgICAgICAgICAgIDxzcGFuPsKpIDIwMjQgT2NlYW4gU291bCBTcGFya2xlcyBBZG1pbiBQb3J0YWw8L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtzdHlsZXMudmVyc2lvbn0+djEuMC4wPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5mb290ZXJSaWdodH0+XHJcbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9hZG1pbi9oZWxwXCIgY2xhc3NOYW1lPXtzdHlsZXMuZm9vdGVyTGlua30+XHJcbiAgICAgICAgICAgICAgICBIZWxwXHJcbiAgICAgICAgICAgICAgPC9MaW5rPlxyXG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW4vcHJpdmFjeVwiIGNsYXNzTmFtZT17c3R5bGVzLmZvb3Rlckxpbmt9PlxyXG4gICAgICAgICAgICAgICAgUHJpdmFjeVxyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2FkbWluL3Rlcm1zXCIgY2xhc3NOYW1lPXtzdHlsZXMuZm9vdGVyTGlua30+XHJcbiAgICAgICAgICAgICAgICBUZXJtc1xyXG4gICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Zvb3Rlcj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTW9iaWxlIE92ZXJsYXkgKi99XHJcbiAgICAgIHtpc01vYmlsZSAmJiBtb2JpbGVNZW51T3BlbiAmJiAoXHJcbiAgICAgICAgPGRpdlxyXG4gICAgICAgICAgY2xhc3NOYW1lPXtzdHlsZXMubW9iaWxlT3ZlcmxheX1cclxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgey8qIE1vYmlsZSBCb3R0b20gTmF2aWdhdGlvbiAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e3N0eWxlcy5tb2JpbGVCb3R0b21OYXZ9PlxyXG4gICAgICAgIDxNb2JpbGVCb3R0b21OYXYgdXNlclJvbGU9e3VzZXI/LnJvbGUgfHwgJ0FkbWluJ30gLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTW9iaWxlIEhhbWJ1cmdlciBNZW51ICovfVxyXG4gICAgICB7LyogPE1vYmlsZUhhbWJ1cmdlck1lbnVcclxuICAgICAgICBpc09wZW49e21vYmlsZU1lbnVPcGVufVxyXG4gICAgICAgIG9uQ2xvc2U9eygpID0+IHNldE1vYmlsZU1lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICB1c2VyUm9sZT17dXNlcj8ucm9sZSB8fCAnQWRtaW4nfVxyXG4gICAgICAgIHVzZXJOYW1lPXtgJHt1c2VyPy5maXJzdE5hbWUgfHwgJyd9ICR7dXNlcj8ubGFzdE5hbWUgfHwgJyd9YC50cmltKCkgfHwgJ0FkbWluIFVzZXInfVxyXG4gICAgICAvPiAqL31cclxuXHJcbiAgICAgIHsvKiBEZWJ1ZyBNb2JpbGUgSW5kaWNhdG9yICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLmRlYnVnTW9iaWxlfT5cclxuICAgICAgICBNb2JpbGU6IHtpc01vYmlsZSA/ICdZRVMnIDogJ05PJ30gfCBXaWR0aDoge3R5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnID8gd2luZG93LmlubmVyV2lkdGggOiAnTi9BJ31cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogU2VjdXJpdHkgTm90aWNlICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnNlY3VyaXR5QmFubmVyfT5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17c3R5bGVzLnNlY3VyaXR5SWNvbn0+8J+UkjwvZGl2PlxyXG4gICAgICAgIDxzcGFuPlNlY3VyZSBBZG1pbiBQb3J0YWwgLSBBbGwgYWN0aW9ucyBhcmUgbG9nZ2VkIGFuZCBtb25pdG9yZWQ8L3NwYW4+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvUFdBTWFuYWdlcj5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIkxpbmsiLCJ0b2FzdCIsIkFkbWluU2lkZWJhciIsIkFkbWluSGVhZGVyIiwiTW9iaWxlQm90dG9tTmF2IiwiUFdBTWFuYWdlciIsInVzZUF1dGgiLCJzdHlsZXMiLCJBZG1pbkxheW91dCIsImNoaWxkcmVuIiwicm91dGVyIiwidXNlciIsImxvYWRpbmciLCJzaWRlYmFyQ29sbGFwc2VkIiwic2V0U2lkZWJhckNvbGxhcHNlZCIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJtb2JpbGVNZW51T3BlbiIsInNldE1vYmlsZU1lbnVPcGVuIiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJwdXNoIiwiaGFuZGxlTG9nb3V0IiwicmVzcG9uc2UiLCJmZXRjaCIsIm1ldGhvZCIsImhlYWRlcnMiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwib2siLCJyZW1vdmVJdGVtIiwic3VjY2VzcyIsIkVycm9yIiwiZXJyb3IiLCJjb25zb2xlIiwidG9nZ2xlU2lkZWJhciIsInRvZ2dsZU1vYmlsZU1lbnUiLCJsb2ciLCJjdXJyZW50Iiwid2lsbEJlIiwiZGl2IiwiY2xhc3NOYW1lIiwibG9hZGluZ0NvbnRhaW5lciIsImxvYWRpbmdTcGlubmVyIiwicCIsImFkbWluTGF5b3V0IiwiY29sbGFwc2VkIiwib25Ub2dnbGUiLCJtYWluQ29udGVudCIsIm9uTG9nb3V0Iiwib25Ub2dnbGVTaWRlYmFyIiwibWFpbiIsInBhZ2VDb250ZW50IiwiZm9vdGVyIiwiYWRtaW5Gb290ZXIiLCJmb290ZXJDb250ZW50IiwiZm9vdGVyTGVmdCIsInNwYW4iLCJ2ZXJzaW9uIiwiZm9vdGVyUmlnaHQiLCJocmVmIiwiZm9vdGVyTGluayIsIm1vYmlsZU92ZXJsYXkiLCJvbkNsaWNrIiwibW9iaWxlQm90dG9tTmF2IiwidXNlclJvbGUiLCJyb2xlIiwiZGVidWdNb2JpbGUiLCJzZWN1cml0eUJhbm5lciIsInNlY3VyaXR5SWNvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./components/admin/AdminLayout.tsx\n"));

/***/ })

});