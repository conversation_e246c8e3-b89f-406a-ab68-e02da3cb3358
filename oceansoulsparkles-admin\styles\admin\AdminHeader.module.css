/* Admin Header Styles */
.adminHeader {
  background: var(--admin-bg-primary);
  border-bottom: 1px solid var(--admin-border-light);
  padding: 0 var(--admin-spacing-lg);
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: var(--admin-z-sticky);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  gap: var(--admin-spacing-md);
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-lg);
}

.sidebarToggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--admin-spacing-sm);
  border-radius: var(--admin-radius-md);
  transition: background var(--admin-transition-normal);
}

.sidebarToggle:hover {
  background: var(--admin-bg-secondary);
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 3px;
  width: 20px;
}

.hamburger span {
  height: 2px;
  background: var(--admin-gray);
  border-radius: 1px;
  transition: all var(--admin-transition-normal);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
  font-size: 0.9rem;
}

.breadcrumbLink {
  color: var(--admin-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color var(--admin-transition-normal);
}

.breadcrumbLink:hover {
  color: var(--admin-primary-dark);
}

.breadcrumbSeparator {
  color: var(--admin-gray);
}

.breadcrumbCurrent {
  color: var(--admin-darker);
  font-weight: 600;
}

.headerCenter {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 500px;
  margin: 0 var(--admin-spacing-lg);
}

.headerRight {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-lg);
}

.quickActions {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-sm);
}

.quickAction {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--admin-bg-secondary);
  border: none;
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  text-decoration: none;
  font-size: 1.1rem;
}

.quickAction:hover {
  background: var(--admin-primary);
  color: white;
  transform: translateY(-1px);
}

.notifications {
  position: relative;
}

.notificationButton {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--admin-bg-secondary);
  border: none;
  border-radius: var(--admin-radius-md);
  cursor: pointer;
  transition: all var(--admin-transition-normal);
  font-size: 1.1rem;
}

.notificationButton:hover {
  background: var(--admin-primary);
  color: white;
}

.notificationBadge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: var(--admin-danger);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 0.7rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--admin-bg-primary);
}

.notificationDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--admin-spacing-sm);
  width: 320px;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10001; /* Above hamburger menu for mobile compatibility */
  overflow: hidden;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
}

.notificationHeader {
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notificationHeader h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--admin-darker);
}

.markAllRead {
  background: none;
  border: none;
  color: var(--admin-primary);
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: color var(--admin-transition-normal);
}

.markAllRead:hover {
  color: var(--admin-primary-dark);
}

.notificationList {
  max-height: 300px;
  overflow-y: auto;
}

.notificationItem {
  display: flex;
  align-items: flex-start;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  transition: background var(--admin-transition-normal);
  cursor: pointer;
}

.notificationItem:hover {
  background: var(--admin-bg-secondary);
}

.notificationItem:last-child {
  border-bottom: none;
}

.notificationIcon {
  font-size: 1.2rem;
  margin-top: 2px;
}

.notificationContent {
  flex: 1;
}

.notificationTitle {
  font-weight: 500;
  color: var(--admin-darker);
  font-size: 0.9rem;
  margin-bottom: 2px;
}

.notificationTime {
  font-size: 0.75rem;
  color: var(--admin-gray);
}

.notificationFooter {
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  border-top: 1px solid var(--admin-border-light);
  text-align: center;
}

.notificationFooter a {
  color: var(--admin-primary);
  text-decoration: none;
  font-size: 0.85rem;
  font-weight: 500;
  transition: color var(--admin-transition-normal);
}

.notificationFooter a:hover {
  color: var(--admin-primary-dark);
}

.userMenu {
  position: relative;
}

.userButton {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--admin-spacing-sm);
  border-radius: var(--admin-radius-md);
  transition: background var(--admin-transition-normal);
}

.userButton:hover {
  background: var(--admin-bg-secondary);
}

.userAvatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-primary-dark));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

.userInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.userName {
  font-weight: 600;
  color: var(--admin-darker);
  font-size: 0.9rem;
  line-height: 1.2;
}

.userRole {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  line-height: 1;
}

.dropdownArrow {
  font-size: 0.7rem;
  color: var(--admin-gray);
  transition: transform var(--admin-transition-normal);
}

.userDropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: var(--admin-spacing-sm);
  width: 240px;
  background: var(--admin-bg-primary);
  border: 1px solid var(--admin-border-light);
  border-radius: var(--admin-radius-lg);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 10001; /* Above hamburger menu for mobile compatibility */
  overflow: hidden;
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
}

.userDropdownHeader {
  padding: var(--admin-spacing-lg);
  border-bottom: 1px solid var(--admin-border-light);
  background: var(--admin-bg-secondary);
}

.userEmail {
  font-size: 0.85rem;
  color: var(--admin-gray);
  margin-bottom: var(--admin-spacing-sm);
  word-break: break-word;
}

.userRoleBadge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--admin-radius-sm);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.userDropdownMenu {
  padding: var(--admin-spacing-sm) 0;
}

.dropdownItem {
  display: flex;
  align-items: center;
  gap: var(--admin-spacing-md);
  padding: var(--admin-spacing-md) var(--admin-spacing-lg);
  color: var(--admin-gray);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--admin-transition-normal);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.dropdownItem:hover {
  background: var(--admin-bg-secondary);
  color: var(--admin-darker);
}

.dropdownIcon {
  font-size: 1rem;
  width: 20px;
  text-align: center;
}

.dropdownDivider {
  height: 1px;
  background: var(--admin-border-light);
  margin: var(--admin-spacing-sm) 0;
}

.logoutItem {
  color: var(--admin-danger);
}

.logoutItem:hover {
  background: rgba(220, 53, 69, 0.1);
  color: var(--admin-danger);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .adminHeader {
    padding: 0 var(--admin-spacing-md);
  }

  .sidebarToggle {
    display: flex;
    min-width: 44px;
    min-height: 44px;
    justify-content: center;
    align-items: center;
    padding: var(--admin-spacing-sm);
  }

  .breadcrumb {
    display: none;
  }

  .headerCenter {
    flex: 1;
    margin: 0 var(--admin-spacing-md);
    max-width: none;
  }

  .quickActions {
    gap: var(--admin-spacing-xs);
  }

  .quickAction {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }

  .userInfo {
    display: none;
  }

  .userButton,
  .notificationButton {
    min-width: 44px;
    min-height: 44px;
    padding: var(--admin-spacing-sm);
  }

  .notificationDropdown,
  .userDropdown {
    width: 280px;
    right: -10px;
    left: auto;
    transform: none;
  }
}

@media (max-width: 480px) {
  .headerRight {
    gap: var(--admin-spacing-md);
  }

  .quickActions {
    display: none;
  }

  .notificationDropdown,
  .userDropdown {
    width: calc(100vw - 20px);
    right: 10px;
    left: auto;
    max-width: 320px;
    transform: translateY(0);
    position: fixed;
    top: 70px; /* Below header */
    margin-top: 0;
  }
}
